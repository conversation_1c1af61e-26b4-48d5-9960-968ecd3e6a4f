"""
title: Multi-Agent Communication Pipeline
author: Custom
description: Pipeline for orchestrating communication between multiple agents
version: 1.0
"""

import asyncio
import json
import requests
from typing import List, Dict, Any, AsyncGenerator
from pydantic import BaseModel, Field


class Pipeline:
    class Valves(BaseModel):
        OPENWEBUI_BASE_URL: str = Field(
            default="http://localhost:3000",
            description="Base URL for OpenWebUI instance"
        )
        API_KEY: str = Field(
            default="",
            description="API key for OpenWebUI"
        )
        RESEARCH_AGENT: str = Field(
            default="research-agent",
            description="ID of the research agent"
        )
        CODE_AGENT: str = Field(
            default="code-agent", 
            description="ID of the code agent"
        )
        AUDIO_AGENT: str = Field(
            default="audio-agent",
            description="ID of the audio agent"
        )
        FILE_AGENT: str = Field(
            default="file-agent",
            description="ID of the file agent"
        )

    def __init__(self):
        self.type = "manifold"
        self.valves = self.Valves()

    def pipes(self) -> List[Dict[str, str]]:
        return [
            {
                "id": "multi-agent-orchestrator",
                "name": "Multi-Agent Orchestrator"
            }
        ]

    async def call_agent(self, agent_id: str, message: str) -> str:
        """Call a specific agent and return their response."""
        try:
            url = f"{self.valves.OPENWEBUI_BASE_URL}/api/chat/completions"
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.valves.API_KEY}" if self.valves.API_KEY else ""
            }
            
            payload = {
                "model": agent_id,
                "messages": [{"role": "user", "content": message}],
                "stream": False
            }
            
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                return f"Error: No response from {agent_id}"
                
        except Exception as e:
            return f"Error calling {agent_id}: {str(e)}"

    def route_request(self, message: str) -> str:
        """Determine which agent should handle the request."""
        message_lower = message.lower()
        
        if any(keyword in message_lower for keyword in ["research", "find", "search", "investigate"]):
            return self.valves.RESEARCH_AGENT
        elif any(keyword in message_lower for keyword in ["code", "program", "script", "function"]):
            return self.valves.CODE_AGENT
        elif any(keyword in message_lower for keyword in ["audio", "music", "sound", "mix"]):
            return self.valves.AUDIO_AGENT
        elif any(keyword in message_lower for keyword in ["file", "folder", "directory", "path"]):
            return self.valves.FILE_AGENT
        else:
            return self.valves.RESEARCH_AGENT  # Default to research agent

    async def pipe(
        self,
        body: dict,
        __user__: dict,
        __task__: str,
        __tools__: dict,
        __event_emitter__: callable
    ) -> AsyncGenerator[str, None]:
        
        messages = body.get("messages", [])
        if not messages:
            yield "No messages provided"
            return
            
        last_message = messages[-1]["content"]
        
        # Check if this is a multi-agent request
        if "call " in last_message.lower() or "ask " in last_message.lower():
            # Parse the agent request
            if "call " in last_message.lower():
                parts = last_message.lower().split("call ", 1)
                if len(parts) > 1:
                    agent_part = parts[1].split(" ", 1)
                    target_agent = agent_part[0]
                    message_to_send = agent_part[1] if len(agent_part) > 1 else "Hello"
            elif "ask " in last_message.lower():
                parts = last_message.lower().split("ask ", 1)
                if len(parts) > 1:
                    agent_part = parts[1].split(" about ", 1)
                    target_agent = agent_part[0]
                    message_to_send = agent_part[1] if len(agent_part) > 1 else "Hello"
            
            # Map friendly names to agent IDs
            agent_mapping = {
                "research": self.valves.RESEARCH_AGENT,
                "code": self.valves.CODE_AGENT,
                "audio": self.valves.AUDIO_AGENT,
                "file": self.valves.FILE_AGENT
            }
            
            agent_id = agent_mapping.get(target_agent, target_agent)
            
            yield f"🤖 Calling {target_agent} agent...\n\n"
            
            response = await self.call_agent(agent_id, message_to_send)
            yield f"**Response from {target_agent} agent:**\n{response}"
            
        else:
            # Route to appropriate agent based on content
            target_agent_id = self.route_request(last_message)
            
            yield f"🤖 Routing to appropriate agent...\n\n"
            
            response = await self.call_agent(target_agent_id, last_message)
            yield f"**Response:**\n{response}"
