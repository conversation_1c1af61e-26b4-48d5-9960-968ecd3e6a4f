# 🤖 Multi-Agent Communication Setup Guide

## ✅ What's Been Configured

1. **Pipelines Service**: Running on port 9099
2. **Multi-Agent Orchestrator**: Advanced pipeline for agent communication
3. **Agent Configuration Helper**: Provides guidance and testing
4. **File Access**: All agents have access to your Nextcloud directories

## 🔧 Next Steps

### 1. Connect OpenWebUI to Pipelines

1. Go to **OpenWebUI Admin Panel > Settings > Connections**
2. Click the `+` button to add a new connection
3. Configure:
   - **API URL**: `http://pipelines:9099`
   - **API Key**: `0p3n-w3bu!`
4. Save and verify the connection

### 2. Configure Agent Model IDs

You need to update the pipeline configuration with your actual agent model IDs. Go to:
**Admin Panel > Settings > Pipelines > Multi-Agent Orchestrator**

Update these values to match your actual models:
- **RESEARCH_AGENT**: Your research model ID (e.g., `llama3.2:latest`)
- **CODE_AGENT**: Your coding model ID (e.g., `qwen2.5-coder:latest`)
- **AUDIO_AGENT**: Your audio engineering model ID
- **FILE_AGENT**: Your file management model ID
- **GENERAL_AGENT**: Your default model ID

### 3. Available Pipeline Models

After connecting, you'll see these new models in your model selector:

1. **🤖 Multi-Agent Orchestrator**
   - Advanced multi-agent coordination
   - Automatic agent collaboration
   - Smart task routing

2. **🔀 Smart Agent Router**
   - Automatically routes requests to appropriate agents
   - Single-agent mode with intelligent routing

3. **⚙️ Agent Configuration**
   - Help and guidance system
   - Agent testing commands
   - Configuration information

## 🎯 How to Use Agent Communication

### Direct Agent Communication

Use these commands with any model:

```
call research about latest AI developments
ask code to create a Python script for file organization
tell audio analyze the mixing on this track
tell file organize my ComfyUI models
```

### Multi-Agent Orchestration

Switch to the **Multi-Agent Orchestrator** model for complex tasks:

```
I need to research AI audio tools and then create a Python script to organize my audio files
```

### Smart Routing

Use the **Smart Agent Router** model for automatic routing:

```
Help me debug this Python code and then organize the files
```

## 🔧 Agent Memory Configuration

Add this memory to each of your specialized agents:

```
User has access to a multi-agent communication system. Other agents available: ResearchAgent (research tasks), CodeAgent (programming), AudioAgent (audio engineering), FileAgent (file management with desktop-commander access to Nextcloud directories). When user mentions collaborating with other agents or requests that require multiple specialties, you can suggest using the Multi-Agent Orchestrator model for coordination. You can also recommend specific agents for tasks outside your specialty.
```

## 🧪 Testing Your Setup

1. **Test Agent Configuration Helper**:
   - Select the "⚙️ Agent Configuration" model
   - Type: `help`

2. **Test Direct Communication**:
   - Use any model and type: `call research about OpenWebUI features`

3. **Test Multi-Agent Orchestration**:
   - Select "🤖 Multi-Agent Orchestrator" model
   - Type: `I need help with both research and coding tasks`

4. **Test Smart Routing**:
   - Select "🔀 Smart Agent Router" model
   - Type: `Help me organize my audio files`

## 🔍 Troubleshooting

### If agents can't communicate:
1. Check that pipelines service is running: `docker logs pipelines`
2. Verify OpenWebUI connection to pipelines
3. Ensure agent model IDs are correct in pipeline configuration
4. Check that target agents exist in your OpenWebUI

### If file access doesn't work:
1. Verify Nextcloud directory is mounted in pipelines container
2. Check desktop-commander MCP configuration
3. Ensure agents have proper memory about file paths

## 🎉 What You Can Now Do

✅ **Agent-to-Agent Communication**: Agents can call each other directly
✅ **Multi-Agent Collaboration**: Complex tasks can be handled by multiple agents
✅ **Smart Routing**: Requests automatically go to the best agent
✅ **File System Access**: All agents can access your Nextcloud directories
✅ **Specialized Workflows**: Create workflows that leverage multiple agent types
✅ **Scalable Architecture**: Easy to add new agents and capabilities

## 🚀 Advanced Features

- **Agent Call Limits**: Prevents infinite loops (configurable)
- **Timeout Protection**: Prevents hanging requests
- **Context Sharing**: Agents share conversation context
- **Error Handling**: Graceful failure handling
- **Logging**: Full audit trail of agent communications

Your multi-agent system is now ready! Start with the Agent Configuration Helper to get familiar with the commands, then experiment with direct agent communication and multi-agent orchestration.
