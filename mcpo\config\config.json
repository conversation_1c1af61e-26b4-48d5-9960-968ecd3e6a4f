{"mcpServers": {"mcp-memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": "/app/data/memory.json"}}, "desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"], "env": {"ALLOWED_PATHS": "/app/nextcloud_data/Music/Studio Sessions;/app/nextcloud_data/Photos;/app/nextcloud_data/Documents/Obsidian Vaults;/app/nextcloud_data/Documents/ComfyUI;/app/nextcloud_data/Documents/Code;/app/nextcloud_data/Documents/Resumes", "PATH_MAPPING": "C:\\Users\\<USER>\\Nextcloud3=/app/nextcloud_data"}}, "mcp-sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "mcp-context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}}}