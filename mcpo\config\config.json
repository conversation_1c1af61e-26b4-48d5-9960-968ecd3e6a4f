{"mcpServers": {"mcp-memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": "/app/data/memory.json"}}, "desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"], "env": {"ALLOWED_PATHS": "C:\\Users\\<USER>\\Nextcloud3\\Music\\Studio Sessions;C:\\Users\\<USER>\\Nextcloud3\\Photos;C:\\Users\\<USER>\\Nextcloud3\\Documents\\Obsidian Vaults;C:\\Users\\<USER>\\Nextcloud3\\Documents\\ComfyUI;C:\\Users\\<USER>\\Nextcloud3\\Documents\\Code;C:\\Users\\<USER>\\Nextcloud3\\Documents\\Resumes"}}, "mcp-sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "mcp-context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}}}