"""
title: Agent Communication Function
author: Custom
description: Allows agents to communicate with each other via OpenWebUI API
version: 1.0
"""

import requests
import json
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field


class Tools:
    def __init__(self):
        pass

    async def call_agent(
        self,
        agent_name: str,
        message: str,
        openwebui_base_url: str = "http://localhost:3000",
        api_key: str = "",
        __user__: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Call another agent in OpenWebUI and get their response.
        
        :param agent_name: Name or ID of the target agent
        :param message: Message to send to the agent
        :param openwebui_base_url: Base URL of OpenWebUI instance
        :param api_key: API key for authentication
        :return: Response from the target agent
        """
        
        try:
            # OpenWebUI API endpoint for chat completions
            url = f"{openwebui_base_url}/api/chat/completions"
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}" if api_key else ""
            }
            
            # Prepare the request payload
            payload = {
                "model": agent_name,
                "messages": [
                    {
                        "role": "user",
                        "content": message
                    }
                ],
                "stream": False
            }
            
            # Make the API call
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()
            
            # Parse the response
            result = response.json()
            
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                return f"Error: No response from agent {agent_name}"
                
        except requests.exceptions.RequestException as e:
            return f"Error calling agent {agent_name}: {str(e)}"
        except Exception as e:
            return f"Unexpected error: {str(e)}"

    async def list_available_agents(
        self,
        openwebui_base_url: str = "http://localhost:3000",
        api_key: str = "",
        __user__: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        List all available agents/models in OpenWebUI.
        
        :param openwebui_base_url: Base URL of OpenWebUI instance
        :param api_key: API key for authentication
        :return: List of available agents
        """
        
        try:
            url = f"{openwebui_base_url}/api/models"
            
            headers = {
                "Authorization": f"Bearer {api_key}" if api_key else ""
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            if "data" in result:
                agents = [model["id"] for model in result["data"]]
                return f"Available agents: {', '.join(agents)}"
            else:
                return "No agents found"
                
        except Exception as e:
            return f"Error listing agents: {str(e)}"

    async def send_message_to_agent(
        self,
        target_agent: str,
        message: str,
        context: str = "",
        __user__: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Send a message to a specific agent with optional context.
        
        :param target_agent: The agent to send the message to
        :param message: The message content
        :param context: Optional context for the message
        :return: Response from the target agent
        """
        
        # Get OpenWebUI configuration from environment or defaults
        openwebui_url = "http://localhost:3000"  # Configure this
        api_key = ""  # Configure this or get from user settings
        
        full_message = f"{context}\n\n{message}" if context else message
        
        return await self.call_agent(
            agent_name=target_agent,
            message=full_message,
            openwebui_base_url=openwebui_url,
            api_key=api_key,
            __user__=__user__
        )


# Initialize the tools
tools = Tools()
