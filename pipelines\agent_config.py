"""
title: Agent Configuration Helper
author: J<PERSON>n
description: Helper pipeline for managing agent configurations and communication
version: 1.0
"""

import json
from typing import List, Dict, Any, AsyncGenerator
from pydantic import BaseModel, Field


class Pipeline:
    class Valves(BaseModel):
        AGENT_REGISTRY: str = Field(
            default=json.dumps({
                "research-agent": {
                    "name": "Research Agent",
                    "description": "Specialized in research, analysis, and information gathering",
                    "capabilities": ["web_search", "data_analysis", "fact_checking"],
                    "tools": ["brave_search", "web_fetch"]
                },
                "code-agent": {
                    "name": "Code Agent", 
                    "description": "Specialized in programming, debugging, and software development",
                    "capabilities": ["code_generation", "debugging", "api_integration"],
                    "tools": ["desktop_commander", "git_tools"]
                },
                "audio-agent": {
                    "name": "Audio Agent",
                    "description": "Specialized in audio engineering, music production, and sound analysis",
                    "capabilities": ["audio_analysis", "mixing", "mastering", "file_management"],
                    "tools": ["desktop_commander", "file_tools"]
                },
                "file-agent": {
                    "name": "File Agent",
                    "description": "Specialized in file management, organization, and data handling",
                    "capabilities": ["file_operations", "data_organization", "backup_management"],
                    "tools": ["desktop_commander", "file_system_tools"]
                }
            }),
            description="JSON registry of available agents and their capabilities"
        )

    def __init__(self):
        self.type = "manifold"
        self.valves = self.Valves()

    def pipelines(self) -> List[Dict[str, str]]:
        return [
            {
                "id": "agent-config",
                "name": "⚙️ Agent Configuration"
            }
        ]

    def get_agent_registry(self) -> Dict[str, Any]:
        """Get the agent registry from valves."""
        try:
            return json.loads(self.valves.AGENT_REGISTRY)
        except json.JSONDecodeError:
            return {}

    def generate_agent_help(self) -> str:
        """Generate help text for agent communication."""
        registry = self.get_agent_registry()
        
        help_text = """# 🤖 Multi-Agent Communication Guide

## Available Agents:

"""
        
        for agent_id, config in registry.items():
            help_text += f"### {config['name']} (`{agent_id}`)\n"
            help_text += f"**Description:** {config['description']}\n"
            help_text += f"**Capabilities:** {', '.join(config['capabilities'])}\n"
            help_text += f"**Tools:** {', '.join(config['tools'])}\n\n"

        help_text += """## Communication Commands:

### Direct Agent Calls:
- `call research about [topic]` - Call the research agent
- `ask code to [task]` - Ask the code agent to perform a task  
- `tell audio [message]` - Send a message to the audio agent
- `tell file [request]` - Send a request to the file agent

### Multi-Agent Orchestration:
Use the **Multi-Agent Orchestrator** model for complex tasks that may require multiple agents.

### Smart Routing:
Use the **Smart Agent Router** model to automatically route your request to the most appropriate agent.

## Examples:

```
call research about latest AI developments
ask code to create a Python script for file organization
tell audio analyze the mixing on this track
tell file organize my ComfyUI models
```

## Agent Collaboration:
Agents can work together automatically when using the orchestrator. For example:
- Research agent can call code agent for implementation
- Audio agent can call file agent for organization
- Any agent can collaborate with others as needed

## File Access:
All agents have access to your configured Nextcloud directories:
- Music/Studio Sessions
- Photos  
- Documents/Obsidian Vaults
- Documents/ComfyUI
- Documents/Code
- Documents/Resumes
"""
        
        return help_text

    async def pipe(
        self,
        body: dict,
        __user__: dict,
        __task__: str,
        __tools__: dict,
        __event_emitter__: callable
    ) -> AsyncGenerator[str, None]:
        
        messages = body.get("messages", [])
        if not messages:
            yield "❌ No messages provided"
            return
            
        last_message = messages[-1]["content"].lower()
        
        if any(keyword in last_message for keyword in ["help", "guide", "how", "agents", "list"]):
            yield self.generate_agent_help()
        
        elif "registry" in last_message or "config" in last_message:
            registry = self.get_agent_registry()
            yield f"## Agent Registry Configuration\n\n```json\n{json.dumps(registry, indent=2)}\n```"
        
        elif "test" in last_message:
            yield """# 🧪 Agent Communication Test

To test agent communication, try these commands:

1. **Test Research Agent:**
   ```
   call research about OpenWebUI features
   ```

2. **Test Code Agent:**
   ```
   ask code to explain Python decorators
   ```

3. **Test Audio Agent:**
   ```
   tell audio about best practices for mixing vocals
   ```

4. **Test File Agent:**
   ```
   tell file to list contents of ComfyUI directory
   ```

5. **Test Multi-Agent Orchestration:**
   Switch to the "Multi-Agent Orchestrator" model and ask:
   ```
   I need to research AI audio tools and then create a Python script to organize my audio files
   ```

Make sure your agents are properly configured in OpenWebUI with the correct model IDs!
"""
        
        else:
            yield """# ⚙️ Agent Configuration Helper

Available commands:
- **help** - Show the complete agent communication guide
- **registry** - Show the current agent registry configuration  
- **test** - Show test commands for agent communication

This helper provides information about your multi-agent system configuration.
"""
