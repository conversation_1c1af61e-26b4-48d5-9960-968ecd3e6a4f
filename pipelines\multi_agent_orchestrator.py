"""
title: Multi-Agent Orchestrator
author: Jalen
description: Advanced pipeline for orchestrating communication between specialized agents
version: 1.0
"""

import asyncio
import json
import aiohttp
import re
from typing import List, Dict, Any, AsyncGenerator, Optional
from pydantic import BaseModel, Field


class Pipeline:
    class Valves(BaseModel):
        OPENWEBUI_BASE_URL: str = Field(
            default="http://open-webui:8080",
            description="Base URL for OpenWebUI instance"
        )
        API_KEY: str = Field(
            default="",
            description="API key for OpenWebUI (get from Admin Panel > Settings > Account)"
        )
        
        # Agent Configuration
        RESEARCH_AGENT: str = Field(
            default="research-agent",
            description="Model ID for research tasks"
        )
        CODE_AGENT: str = Field(
            default="code-agent", 
            description="Model ID for programming tasks"
        )
        AUDIO_AGENT: str = Field(
            default="audio-agent",
            description="Model ID for audio engineering tasks"
        )
        FILE_AGENT: str = Field(
            default="file-agent",
            description="Model ID for file management tasks"
        )
        GENERAL_AGENT: str = Field(
            default="llama3.2:latest",
            description="Default model for general tasks"
        )
        
        # Communication Settings
        MAX_AGENT_CALLS: int = Field(
            default=5,
            description="Maximum number of agent calls per request"
        )
        TIMEOUT_SECONDS: int = Field(
            default=30,
            description="Timeout for agent calls in seconds"
        )

    def __init__(self):
        self.type = "manifold"
        self.valves = self.Valves()
        self.agent_call_count = 0

    def pipelines(self) -> List[Dict[str, str]]:
        return [
            {
                "id": "multi-agent-orchestrator",
                "name": "🤖 Multi-Agent Orchestrator"
            },
            {
                "id": "agent-router",
                "name": "🔀 Smart Agent Router"
            }
        ]

    async def call_agent(self, agent_id: str, message: str, context: str = "") -> str:
        """Call a specific agent and return their response."""
        if self.agent_call_count >= self.valves.MAX_AGENT_CALLS:
            return f"❌ Maximum agent calls ({self.valves.MAX_AGENT_CALLS}) reached"
            
        self.agent_call_count += 1
        
        try:
            url = f"{self.valves.OPENWEBUI_BASE_URL}/api/chat/completions"
            
            headers = {
                "Content-Type": "application/json"
            }
            
            if self.valves.API_KEY:
                headers["Authorization"] = f"Bearer {self.valves.API_KEY}"
            
            # Prepare the message with context
            full_message = f"{context}\n\n{message}" if context else message
            
            payload = {
                "model": agent_id,
                "messages": [{"role": "user", "content": full_message}],
                "stream": False,
                "temperature": 0.7
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url, 
                    headers=headers, 
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=self.valves.TIMEOUT_SECONDS)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        if "choices" in result and len(result["choices"]) > 0:
                            return result["choices"][0]["message"]["content"]
                        else:
                            return f"❌ No response from {agent_id}"
                    else:
                        error_text = await response.text()
                        return f"❌ Error calling {agent_id}: HTTP {response.status} - {error_text}"
                        
        except asyncio.TimeoutError:
            return f"❌ Timeout calling {agent_id} after {self.valves.TIMEOUT_SECONDS}s"
        except Exception as e:
            return f"❌ Error calling {agent_id}: {str(e)}"

    def parse_agent_request(self, message: str) -> Optional[Dict[str, str]]:
        """Parse agent communication requests from user message."""
        message_lower = message.lower()
        
        # Pattern: "call [agent] about [topic]"
        call_match = re.search(r'call\s+(\w+)\s+about\s+(.+)', message_lower)
        if call_match:
            return {
                "action": "call",
                "agent": call_match.group(1),
                "message": call_match.group(2)
            }
        
        # Pattern: "ask [agent] to [task]"
        ask_match = re.search(r'ask\s+(\w+)\s+to\s+(.+)', message_lower)
        if ask_match:
            return {
                "action": "ask",
                "agent": ask_match.group(1),
                "message": ask_match.group(2)
            }
        
        # Pattern: "tell [agent] [message]"
        tell_match = re.search(r'tell\s+(\w+)\s+(.+)', message_lower)
        if tell_match:
            return {
                "action": "tell",
                "agent": tell_match.group(1),
                "message": tell_match.group(2)
            }
        
        return None

    def route_to_agent(self, message: str) -> str:
        """Determine which agent should handle the request based on content."""
        message_lower = message.lower()
        
        # Research keywords
        if any(keyword in message_lower for keyword in [
            "research", "find", "search", "investigate", "analyze", "study", "explore"
        ]):
            return self.valves.RESEARCH_AGENT
        
        # Code keywords
        elif any(keyword in message_lower for keyword in [
            "code", "program", "script", "function", "debug", "python", "javascript", "api"
        ]):
            return self.valves.CODE_AGENT
        
        # Audio keywords
        elif any(keyword in message_lower for keyword in [
            "audio", "music", "sound", "mix", "master", "studio", "recording", "comfyui"
        ]):
            return self.valves.AUDIO_AGENT
        
        # File management keywords
        elif any(keyword in message_lower for keyword in [
            "file", "folder", "directory", "path", "nextcloud", "documents", "photos"
        ]):
            return self.valves.FILE_AGENT
        
        else:
            return self.valves.GENERAL_AGENT

    def get_agent_mapping(self) -> Dict[str, str]:
        """Get mapping of friendly names to agent IDs."""
        return {
            "research": self.valves.RESEARCH_AGENT,
            "researcher": self.valves.RESEARCH_AGENT,
            "code": self.valves.CODE_AGENT,
            "coder": self.valves.CODE_AGENT,
            "programmer": self.valves.CODE_AGENT,
            "audio": self.valves.AUDIO_AGENT,
            "music": self.valves.AUDIO_AGENT,
            "sound": self.valves.AUDIO_AGENT,
            "file": self.valves.FILE_AGENT,
            "files": self.valves.FILE_AGENT,
            "filesystem": self.valves.FILE_AGENT,
            "general": self.valves.GENERAL_AGENT,
            "default": self.valves.GENERAL_AGENT
        }

    async def pipe(
        self,
        body: dict,
        __user__: dict,
        __task__: str,
        __tools__: dict,
        __event_emitter__: callable
    ) -> AsyncGenerator[str, None]:
        
        # Reset agent call counter for each request
        self.agent_call_count = 0
        
        messages = body.get("messages", [])
        if not messages:
            yield "❌ No messages provided"
            return
            
        last_message = messages[-1]["content"]
        model_id = body.get("model", "")
        
        # Check if this is a direct agent communication request
        agent_request = self.parse_agent_request(last_message)
        
        if agent_request:
            # Direct agent communication
            agent_mapping = self.get_agent_mapping()
            target_agent = agent_mapping.get(
                agent_request["agent"], 
                agent_request["agent"]
            )
            
            yield f"🤖 **Calling {agent_request['agent']} agent...**\n\n"
            
            # Add context about the user and previous conversation
            context = f"You are being called by another agent. Previous conversation context:\n"
            if len(messages) > 1:
                context += f"Last few messages: {messages[-3:]}"
            
            response = await self.call_agent(
                target_agent, 
                agent_request["message"],
                context
            )
            
            yield f"**Response from {agent_request['agent']} agent:**\n\n{response}\n\n"
            yield f"---\n*Agent call completed ({self.agent_call_count}/{self.valves.MAX_AGENT_CALLS})*"
            
        elif "multi-agent" in model_id or "orchestrator" in model_id:
            # Multi-agent orchestration mode
            yield f"🎭 **Multi-Agent Orchestrator Active**\n\n"
            
            # Analyze the request and determine if multiple agents are needed
            if any(keyword in last_message.lower() for keyword in [
                "and", "also", "then", "after", "both", "multiple", "collaborate"
            ]):
                yield "🔍 **Analyzing request for multi-agent collaboration...**\n\n"
                
                # Route to primary agent first
                primary_agent = self.route_to_agent(last_message)
                yield f"📍 **Primary agent: {primary_agent}**\n\n"
                
                primary_response = await self.call_agent(primary_agent, last_message)
                yield f"**Primary Response:**\n{primary_response}\n\n"
                
                # Check if primary agent suggests collaboration
                if any(keyword in primary_response.lower() for keyword in [
                    "need", "require", "should", "recommend", "suggest", "help"
                ]):
                    yield "🤝 **Initiating agent collaboration...**\n\n"
                    
                    # Get secondary agent suggestion
                    secondary_agent = self.valves.GENERAL_AGENT
                    if primary_agent != self.valves.CODE_AGENT and "code" in primary_response.lower():
                        secondary_agent = self.valves.CODE_AGENT
                    elif primary_agent != self.valves.FILE_AGENT and "file" in primary_response.lower():
                        secondary_agent = self.valves.FILE_AGENT
                    
                    if secondary_agent != self.valves.GENERAL_AGENT:
                        collaboration_context = f"Another agent provided this response: {primary_response}\n\nPlease provide additional assistance for: {last_message}"
                        secondary_response = await self.call_agent(secondary_agent, collaboration_context)
                        yield f"**Collaborative Response:**\n{secondary_response}\n\n"
            
            else:
                # Single agent routing
                target_agent = self.route_to_agent(last_message)
                yield f"🎯 **Routing to: {target_agent}**\n\n"
                
                response = await self.call_agent(target_agent, last_message)
                yield f"**Response:**\n{response}\n\n"
            
            yield f"---\n*Orchestration completed ({self.agent_call_count}/{self.valves.MAX_AGENT_CALLS})*"
            
        else:
            # Smart routing mode
            target_agent = self.route_to_agent(last_message)
            yield f"🔀 **Smart routing to: {target_agent}**\n\n"
            
            response = await self.call_agent(target_agent, last_message)
            yield f"{response}"
