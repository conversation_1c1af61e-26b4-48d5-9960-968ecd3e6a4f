{"language": "en", "segments": [{"end": 5.44, "start": 0.0, "text": " I'm going to be giving you an Excel spreadsheet."}, {"end": 9.56, "start": 5.44, "text": " I have a list of artists in a session's folder."}, {"end": 18.52, "start": 9.56, "text": " I need you to do research on each song from each artist to find potential upload status,"}, {"end": 25.2, "start": 18.52, "text": " YouTube video, music video, slash, been uploaded on music distribution platforms."}, {"end": 29.560000000000002, "start": 25.2, "text": " I need you to craft a template for the Excel format."}, {"end": 38.68000000000001, "start": 29.560000000000002, "text": " I need a two list, the artist, the name of the song, if it had a YouTube video, what"}, {"end": 41.440000000000005, "start": 38.68000000000001, "text": " platforms it's on, and that's it."}], "text": "I'm going to be giving you an Excel spreadsheet.  I have a list of artists in a session's folder.  I need you to do research on each song from each artist to find potential upload status,  YouTube video, music video, slash, been uploaded on music distribution platforms.  I need you to craft a template for the Excel format.  I need a two list, the artist, the name of the song, if it had a YouTube video, what  platforms it's on, and that's it."}